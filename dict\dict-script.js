// ========== DICT-SCRIPT.JS (MODIFICADO) ==========
document.addEventListener('DOMContentLoaded', async () => {
    // ========== URLs DE DATOS ==========
    // URLs de JMDICT y KANJIDIC eliminadas, se usa la API
    const SEARCH_API_URL = 'search.php'; // ¡NUEVO!
    const KRADFILE_URL = '../data/kradfile-3.6.1.json';
    const KANJI_JSON_URL = '../data/kanji.json';
    const TATOEBA_ES_URL = '../data/tatoeba-esp.tsv';
    const JLPT_URL = '../data/jlpt.json';

    // ========== ELEMENTOS DEL DOM (sin cambios) ==========
    const searchInput = document.getElementById('dictionary-search-input');
    const searchButton = document.getElementById('dictionary-search-button');
    const vocabResultsList = document.getElementById('vocabulary-results-list');
    const searchSuggestionContainer = document.getElementById('search-suggestion-container');
    const searchOriginalQueryLink = document.getElementById('search-original-query-link');
    const kanjiResultsList = document.getElementById('kanji-results-list');
    const vocabResultsTitle = document.getElementById('vocabulary-results-title');
    const kanjiResultsTitle = document.getElementById('kanji-results-title');
    const dictionaryInitialMessage = document.getElementById('dictionary-initial-message');
    const searchResultsContainer = document.querySelector('.search-results-container');
    const searchResultsTabsContainer = document.getElementById('search-results-tabs-container');
    const searchResultsTabButtons = document.querySelectorAll('.search-results-tab-button');
    const vocabShowMoreButton = document.getElementById('vocab-show-more-button');
    const kanjiShowMoreButton = document.getElementById('kanji-show-more-button');
    const mainView = document.getElementById('dictionary-main-view');
    const vocabDetailView = document.getElementById('dictionary-vocab-detail-view');
    const kanjiDetailView = document.getElementById('dictionary-kanji-detail-view');
    const vocabDetailBackButton = document.getElementById('vocab-detail-back-button');
    const vocabDetailTermDisplayWrapper = vocabDetailView.querySelector('.vocab-detail-term-display-wrapper');
    const vocabDetailTermDisplay = vocabDetailView.querySelector('.vocab-detail-term-display');
    const vocabDetailTagsContainer = document.getElementById('vocab-detail-tags-container');
    const vocabDetailSensesContainer = document.getElementById('vocab-detail-senses-container');
    const vocabDetailSensesTitle = document.getElementById('vocab-detail-senses-title');
    const vocabDetailSensesList = document.getElementById('vocab-detail-senses-list');
    const vocabDetailEnglishSensesContainer = document.getElementById('vocab-detail-english-senses-container');
    const vocabDetailEnglishSensesList = document.getElementById('vocab-detail-english-senses-list');
    const vocabDetailKanjiGrid = document.getElementById('vocab-detail-kanji-grid');
    const vocabDetailExamplesList = document.getElementById('vocab-detail-examples-list');
    const vocabDetailExamplesTitle = document.getElementById('vocab-detail-examples-title');
    const vocabExamplesShowMoreButton = document.getElementById('vocab-examples-show-more-button');
    const vocabDetailLikeButton = document.getElementById('vocab-detail-like-button');
    const vocabDetailAddCollectionButton = document.getElementById('vocab-detail-add-collection-button');
    const kanjiDetailBackButton = document.getElementById('kanji-detail-back-button-dict');
    const kanjiDetailMainMeaningHeader = document.getElementById('kanji-detail-main-meaning-header-dict');
    const kanjiDetailAnimationTarget = document.getElementById('kanji-detail-animation-target-dict');
    const kanjiDetailMainInfo = document.getElementById('kanji-detail-main-info-dict');
    const kanjiDetailCharForData = kanjiDetailMainInfo.querySelector('.kanji-char-for-data-dict');
    const kanjiDetailJlpt = document.getElementById('kanji-detail-jlpt-dict');
    const kanjiDetailFrequency = document.getElementById('kanji-detail-frequency-dict');
    const kanjiDetailStrokes = document.getElementById('kanji-detail-strokes-dict');
    const kanjiDetailOnYomi = document.getElementById('kanji-detail-on-yomi-dict');
    const kanjiDetailKunYomi = document.getElementById('kanji-detail-kun-yomi-dict');
    const kanjiDetailLikeButtonDict = document.getElementById('kanji-detail-like-button-dict');
    const kanjiDetailAddCollectionButtonDict = document.getElementById('kanji-detail-add-collection-button-dict');
    const kanjiMeaningsTab = document.getElementById('meanings-dict');
    const kanjiRadicalsTab = document.getElementById('radicals-dict');
    const kanjiUserNotesTextarea = document.getElementById('kanji-detail-user-notes-dict');
    const kanjiEditNotesButton = document.getElementById('kanji-detail-edit-notes-dict');
    const kanjiSaveNotesButton = document.getElementById('kanji-detail-save-notes-dict');
    const kanjiResetNotesButton = document.getElementById('kanji-detail-reset-notes-dict');
    const kanjiCommonWordsList = document.getElementById('kanji-detail-common-words-list-dict');
    const kanjiAllWordsList = document.getElementById('kanji-detail-all-words-list-dict');
    const kanjiCommonWordsShowMoreButton = document.getElementById('kanji-common-words-show-more-button');
    const kanjiAllWordsShowMoreButton = document.getElementById('kanji-all-words-show-more-button');
    const addToCollectionModal = document.getElementById('dict-add-to-collection-modal');
    const addToCollectionModalClose = addToCollectionModal.querySelector('.modal-fusoku-close-button');
    const existingCollectionsListModal = document.getElementById('dict-existing-collections-list-modal');
    const newCollectionNameModalInput = document.getElementById('dict-new-collection-name-modal');
    const createAndAddToNewCollectionBtnModal = document.getElementById('dict-create-and-add-to-new-collection-button-modal');
    const addToCollectionFeedback = document.getElementById('dict-add-to-collection-feedback');
    const modalCollectionTitle = document.getElementById('dict-modal-collection-title');

    // ========== ESTADO DE LA APLICACIÓN ==========
    let jmdictData = []; // Ahora se llena bajo demanda por búsqueda
    let kanjidicData = {}; // Ahora se llena bajo demanda por búsqueda
    let allFoundKanjiDetails = {}; // Cache para detalles de kanji ya buscados
    let allFoundVocabDetails = {}; // Cache para detalles de vocab ya buscados
    let kradfileData = {};
    let kanjiJsonData = {};
    let tatoebaSentences = [];
    let jlptDataRaw = [];
    let jlptLookupMap = new Map();
    let userProgress = {
        theme: 'light',
        dictionaryCollections: {
            vocab: [{ name: "Favoritos Vocab", items: [], isSystem: true }],
            kanji: [{ name: "Favoritos Kanji (Diccionario)", items: [], isSystem: true }]
        },
        kanjiUserNotes: {}
    };
    let currentDetailTerm = null;
    let currentDetailKanji = null;
    let dmakInstanceDict = null;
    let dmakIsAnimatingOnClickDict = false;
    let navigationHistory = [];
    let currentItemForModal = null;
    let currentCollectionTypeForModal = 'vocab';
    const USUALLY_KANA_WRITING_TAG = " 通常は仮名書き ";
    const ITEMS_PER_LOAD = 20;
    let currentVocabResults = [];
    let currentKanjiResults = [];
    let currentVocabOffset = 0;
    let currentKanjiOffset = 0;
    let currentExampleSentences = [];
    let currentExampleOffset = 0;
    let currentKanjiCommonWords = [];
    let currentKanjiCommonWordsOffset = 0;
    let currentKanjiAllWords = [];
    let currentKanjiAllWordsOffset = 0;

    // ========== CSV/DB PARSING & TRANSFORMATION FUNCTIONS (MODIFICADO) ==========

    // Ya no se necesita parseCSVLine, parseJmdictCSV, convertCSVRowToJmdictEntry
    // En su lugar, añadimos funciones para transformar la respuesta del servidor

    /**
     * Limpia el texto de una definición eliminando metadatos JSON y caracteres no deseados.
     * @param {string} glossText - El texto de la definición que puede contener metadatos.
     * @returns {string} El texto limpio de la definición.
     */
    function cleanGlossText(glossText) {
        if (!glossText || glossText.trim() === '') return '';

        let cleanText = glossText.trim();

        // Remover comillas externas si están presentes
        if (cleanText.startsWith('"') && cleanText.endsWith('"')) {
            cleanText = cleanText.slice(1, -1);
        }

        // Extraer solo el texto de la definición, eliminando metadatos JSON
        // Formato: "texto de la definición {\"pS\":[\"n\"],\"aK\":[\"*\"],\"aR\":[\"*\"]}"
        const match = cleanText.match(/^([^{]+)(\{.*\})?$/);
        if (match) {
            let glossText = match[1].trim();

            // Remover comillas internas si están presentes
            if (glossText.startsWith('"') && glossText.endsWith('"')) {
                glossText = glossText.slice(1, -1);
            }

            return glossText;
        }

        // Si no hay metadatos JSON, devolver el texto limpio
        return cleanText;
    }

    /**
     * Transforma una fila de la tabla `registros_full` a la estructura de objeto JMdict que espera el script.
     * @param {object} dbRow - Una fila de la base de datos.
     * @returns {object} Un objeto con formato similar al JMdict original.
     */
    function transformDbVocabEntry(dbRow) {
        const entry = {
            id: dbRow.id,
            KJ: [],
            KN: [],
            SE: []
            // Otros campos como jlptLevel se añadirán después
        };

        // Procesar Kanji (KJ_tx)
        if (dbRow.KJ_tx) {
            dbRow.KJ_tx.split('|').forEach(kanjiText => {
                if (kanjiText.trim()) {
                    entry.KJ.push({
                        tx: kanjiText.trim(),
                        // Mock de datos que ya no vienen de la DB pero el script puede necesitar
                        co: dbRow.KJ_co || 0,
                        tg: dbRow.KJ_tg ? dbRow.KJ_tg.split(',') : []
                    });
                }
            });
        }

        // Procesar Kana (KN_tx)
        if (dbRow.KN_tx) {
            entry.KN.push({
                tx: dbRow.KN_tx.trim(),
                co: dbRow.KN_co || 0,
                tg: dbRow.KN_tg ? dbRow.KN_tg.split(',') : [],
                aK: ["*"] // Mock
            });
        }
        
        // Procesar Sentidos (SE) y Glosarios (GL)
        // Dividir las definiciones por | para crear sentidos separados y limpiar metadatos
        const englishDefinitions = dbRow.SE_GL_1_tx ? dbRow.SE_GL_1_tx.split('|').map(def => cleanGlossText(def)).filter(def => def) : [];
        const spanishDefinitions = dbRow.SE_GL_2_tx ? dbRow.SE_GL_2_tx.split('|').map(def => cleanGlossText(def)).filter(def => def) : [];

        // Determinar el número máximo de definiciones para crear los sentidos
        const maxDefinitions = Math.max(englishDefinitions.length, spanishDefinitions.length);

        // Si no hay definiciones, crear un sentido vacío
        if (maxDefinitions === 0) {
            const sense = {
                pS: dbRow.SE_pS ? dbRow.SE_pS.split(',') : [],
                ms: dbRow.SE_ms ? dbRow.SE_ms.split(',') : [],
                in: dbRow.SE_in ? dbRow.SE_in.split(',') : [],
                fd: dbRow.SE_fd ? dbRow.SE_fd.split(',') : [],
                dt: dbRow.SE_dt ? dbRow.SE_dt.split(',') : [],
                GL: []
            };
            entry.SE.push(sense);
        } else {
            // Crear un sentido separado para cada definición
            for (let i = 0; i < maxDefinitions; i++) {
                const sense = {
                    pS: dbRow.SE_pS ? dbRow.SE_pS.split(',') : [],
                    ms: dbRow.SE_ms ? dbRow.SE_ms.split(',') : [],
                    in: dbRow.SE_in ? dbRow.SE_in.split(',') : [],
                    fd: dbRow.SE_fd ? dbRow.SE_fd.split(',') : [],
                    dt: dbRow.SE_dt ? dbRow.SE_dt.split(',') : [],
                    GL: []
                };

                // Añadir definición en inglés si existe
                if (englishDefinitions[i]) {
                    sense.GL.push({ ln: 'en', tx: englishDefinitions[i] });
                }

                // Añadir definición en español si existe
                if (spanishDefinitions[i]) {
                    sense.GL.push({ ln: 'es', tx: spanishDefinitions[i] });
                }

                entry.SE.push(sense);
            }
        }
        
        // Cachear el objeto transformado
        allFoundVocabDetails[entry.id] = entry;

        return entry;
    }

    /**
     * Transforma una fila de la tabla `kanji_dict` a la estructura de objeto Kanjidic que espera el script.
     * @param {object} dbRow - Una fila de la base de datos.
     * @returns {object} Un objeto con formato similar al Kanjidic original.
     */
    function transformDbKanjiEntry(dbRow) {
        const entry = {
            l: dbRow.li_tx, // literal
            ms: { // miscellaneous
                sc: [dbRow.ms_sc_vl], // stroke count
                fr: dbRow.ms_fr_vl || null // frequency
            },
            rm: { // reading_meaning
                g: [{ // group
                    rd: [], // reading
                    mn: []  // meaning
                }]
            }
        };

        // Procesar lecturas On'yomi
        if (dbRow.rM_g_rd_on_vl) {
            dbRow.rM_g_rd_on_vl.split('|').forEach(reading => {
                if (reading.trim()) entry.rm.g[0].rd.push({ t: 'ja_on', v: reading.trim() });
            });
        }

        // Procesar lecturas Kun'yomi
        if (dbRow.rM_g_rd_kun_vl) {
            dbRow.rM_g_rd_kun_vl.split('|').forEach(reading => {
                if (reading.trim()) entry.rm.g[0].rd.push({ t: 'ja_kun', v: reading.trim() });
            });
        }

        // Procesar significados en Inglés
        if (dbRow.mn_ln_en_vl) {
            dbRow.mn_ln_en_vl.split('|').forEach(meaning => {
                if (meaning.trim()) entry.rm.g[0].mn.push({ ln: 'en', v: meaning.trim() });
            });
        }
        
        // Procesar significados en Español
        if (dbRow.mn_ln_es_vl) {
            dbRow.mn_ln_es_vl.split('|').forEach(meaning => {
                if (meaning.trim()) entry.rm.g[0].mn.push({ ln: 'es', v: meaning.trim() });
            });
        }
        
        // Cachear el objeto transformado
        allFoundKanjiDetails[entry.l] = entry;

        return entry;
    }


    // ========== PARSE URL HASH & HANDLE NAVIGATION (sin cambios) ==========
    function parseLocationHashDictionary() {
        const hash = window.location.hash.substring(1); // Remove '#'
        if (!hash) return null;

        const params = new URLSearchParams(hash);
        const type = params.get('type');
        const id = params.get('id');

        if (type && id) {
            return { type: decodeURIComponent(type), id: decodeURIComponent(id) };
        }
        return null;
    }

    async function handleDictionaryNavigation() { // Convertida a async
        const navigationData = parseLocationHashDictionary();
        if (navigationData) {
            navigationHistory = [{ view: 'main', id: null }];

            if (navigationData.type === 'wordJmdict') {
                // Si la palabra no está en caché, la buscamos
                if (!allFoundVocabDetails[navigationData.id]) {
                    await performSearch(navigationData.id, true); // Forzar búsqueda por ID
                }
                showView('vocabDetail', navigationData.id);
                return true;

            } else if (navigationData.type === 'kanjiKanjidic' || navigationData.type === 'kanjiFusoku') {
                // Si el kanji no está en caché, lo buscamos
                if (!allFoundKanjiDetails[navigationData.id]) {
                    await performSearch(navigationData.id);
                }
                showView('kanjiDetail', navigationData.id);
                return true;
            }
        }
        return false;
    }

    // ========== CARGA DE DATOS Y PREPROCESAMIENTO (MODIFICADO) ==========
    async function loadAllDictionaryData() {
        try {
            console.log("Iniciando carga de datos estáticos...");
            const [
                kradfileResponse,
                kanjiJsonResponse,
                tatoebaResponse,
                jlptResponse
            ] = await Promise.all([
                fetch(KRADFILE_URL),
                fetch(KANJI_JSON_URL),
                fetch(TATOEBA_ES_URL),
                fetch(JLPT_URL)
            ]);

            // Se eliminaron las llamadas a JMDICT y KANJIDIC
            if (!kradfileResponse.ok) throw new Error(`Kradfile: ${kradfileResponse.statusText}`);
            if (!kanjiJsonResponse.ok) throw new Error(`Kanji.json: ${kanjiJsonResponse.statusText}`);
            if (!tatoebaResponse.ok) throw new Error(`Tatoeba: ${tatoebaResponse.statusText}`);
            if (!jlptResponse.ok) throw new Error(`JLPT: ${jlptResponse.statusText}`);

            const jlptRaw = await jlptResponse.json();
            const kradfileRawData = await kradfileResponse.json();

            // Lógica de Kradfile (sin cambios)
            if (kradfileRawData && typeof kradfileRawData === 'object' && kradfileRawData.kanji && typeof kradfileRawData.kanji === 'object') {
                kradfileData = kradfileRawData.kanji;
            } else if (kradfileRawData && typeof kradfileRawData === 'object' && kradfileRawData.data && typeof kradfileRawData.data === 'object') {
                kradfileData = kradfileRawData.data;
            } else {
                kradfileData = kradfileRawData;
            }

            kanjiJsonData = await kanjiJsonResponse.json();
            const tatoebaText = await tatoebaResponse.text();
            jlptDataRaw = jlptRaw;

            // Lógica de JLPT (sin cambios)
            if (Array.isArray(jlptDataRaw)) {
                jlptDataRaw.forEach(entry => {
                    if (Array.isArray(entry) && entry.length === 3 && typeof entry[0] === 'string' && typeof entry[1] === 'string' && Array.isArray(entry[2]) && entry[2].length > 0 && typeof entry[2][0] === 'string') {
                        const wordOrKanjiFormJlpt = entry[0];
                        const kanaReadingJlpt = entry[1];
                        const jlptLevel = entry[2][0];
                        if (wordOrKanjiFormJlpt === kanaReadingJlpt) {
                            jlptLookupMap.set(kanaReadingJlpt, jlptLevel);
                        } else {
                            const key = wordOrKanjiFormJlpt + "#" + kanaReadingJlpt;
                            jlptLookupMap.set(key, jlptLevel);
                        }
                    }
                });
            }

            // Lógica de Tatoeba (sin cambios)
            tatoebaSentences = tatoebaText.split('\n').map(line => {
                const parts = line.split('\t');
                if (parts.length >= 4) {
                    return { jp: parts[1].trim(), es: parts[3].trim() };
                } else if (parts.length === 2) {
                    return { jp: parts[0].trim(), es: parts[1].trim() };
                }
                return null;
            }).filter(s => s && s.jp && s.es);

            console.log("Datos estáticos (JLPT, Krad, Tatoeba) cargados correctamente.");
            
            // Ya no se procesa JMDICT/KANJIDIC aquí

            // Lógica de Tema y Progreso de Usuario (sin cambios)
            if (document.body.classList.contains('dark-mode')) userProgress.theme = 'dark';
            else userProgress.theme = 'light';
            
            const themeObserver = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    if (mutation.attributeName === 'class') {
                        userProgress.theme = document.body.classList.contains('dark-mode') ? 'dark' : 'light';
                        if (kanjiDetailView.classList.contains('active-dict-view') && currentDetailKanji) {
                            initDmakDict(currentDetailKanji.l);
                        }
                    }
                });
            });
            themeObserver.observe(document.body, { attributes: true });

            loadUserProgressLocal();
            initComponents();
            if (searchResultsContainer) searchResultsContainer.style.display = 'none';
            if (dictionaryInitialMessage) dictionaryInitialMessage.style.display = 'block';

        } catch (error) {
            console.error("Error crítico al cargar datos del diccionario:", error);
            if (mainView) {
                 mainView.innerHTML = `<p class="error-message">Error al cargar los datos del diccionario: ${error.message}. Por favor, recarga la página o revisa la consola.</p>`;
            }
        }
    }

    // Lógica de findJlptForTerm, loadUserProgressLocal, saveUserProgressLocal (sin cambios)
    function findJlptForTerm(jmdictEntry) {
        for (const kjElement of jmdictEntry.KJ) {
            const kanjiForm = kjElement.tx;
            if (!kanjiForm) continue;

            for (const knElement of jmdictEntry.KN) {
                const kanaReading = knElement.tx;
                if (!kanaReading) continue;

                const keyForKanjiCombo = kanjiForm + "#" + kanaReading;
                if (jlptLookupMap.has(keyForKanjiCombo)) {
                    return jlptLookupMap.get(keyForKanjiCombo);
                }
            }
        }
        for (const knElement of jmdictEntry.KN) {
            const kanaReading = knElement.tx;
            if (!kanaReading) continue;

            const primaryKanjiForm = jmdictEntry.KJ[0]?.tx;
            if (primaryKanjiForm && primaryKanjiForm === kanaReading) {
                if (jlptLookupMap.has(kanaReading)) {
                    return jlptLookupMap.get(kanaReading);
                }
            }
            if (jmdictEntry.KJ.length === 0 || jmdictEntry.KJ.every(k => !k.tx)) {
                if (jlptLookupMap.has(kanaReading)) {
                    return jlptLookupMap.get(kanaReading);
                }
            }
        }
        return null;
    }

    function loadUserProgressLocal() {
        const saved = localStorage.getItem('fusokuDictionaryProgress');
        if (saved) {
            const parsed = JSON.parse(saved);
            const currentTheme = userProgress.theme;
            userProgress = { ...userProgress, ...parsed };
            userProgress.theme = currentTheme;

            if (!userProgress.dictionaryCollections.vocab.find(c => c.name === "Favoritos Vocab" && c.isSystem)) {
                userProgress.dictionaryCollections.vocab.unshift({ name: "Favoritos Vocab", items: [], isSystem: true });
            }
            if (!userProgress.dictionaryCollections.kanji.find(c => c.name === "Favoritos Kanji (Diccionario)" && c.isSystem)) {
                userProgress.dictionaryCollections.kanji.unshift({ name: "Favoritos Kanji (Diccionario)", items: [], isSystem: true });
            }
        } else {
             userProgress.dictionaryCollections = {
                vocab: [{ name: "Favoritos Vocab", items: [], isSystem: true }],
                kanji: [{ name: "Favoritos Kanji (Diccionario)", items: [], isSystem: true }]
            };
        }
        if (typeof userProgress.kanjiUserNotes !== 'object' || userProgress.kanjiUserNotes === null) {
            userProgress.kanjiUserNotes = {};
        }
    }

    function saveUserProgressLocal() {
        localStorage.setItem('fusokuDictionaryProgress', JSON.stringify(userProgress));
    }
    
    // Lógica de searchWholeWordInText (sin cambios)
    function searchWholeWordInText(text, searchTerm) {
        if (!text || !searchTerm) return false;
        const regex = new RegExp(`\\b${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
        return regex.test(text);
    }

    // ========== LÓGICA DE BÚSQUEDA (MODIFICADO) ==========
    async function performSearch(forceNonJapaneseSearch = false) {
        window.scrollTo(0, 0);
        const currentSearchInputValue = searchInput.value.trim();
        if (!currentSearchInputValue) {
            clearSearchResults();
            if (searchResultsContainer) searchResultsContainer.style.display = 'none';
            if (searchResultsTabsContainer) searchResultsTabsContainer.style.display = 'none';
            if (dictionaryInitialMessage) dictionaryInitialMessage.style.display = 'block';
            return;
        }

        if (dictionaryInitialMessage) dictionaryInitialMessage.style.display = 'none';
        if (searchResultsContainer) searchResultsContainer.style.display = 'flex';
        handleResponsiveTabs();

        navigationHistory = [];
        showView('main');

        const originalQueryForDetection = currentSearchInputValue;
        let queryForSearch = originalQueryForDetection;
        let searchAsJapanese = false;
        let queryWasConverted = false;

        // Lógica de detección de idioma (sin cambios)
        if (forceNonJapaneseSearch) {
            searchAsJapanese = false;
        } else if (originalQueryForDetection.toLowerCase().includes('x') || originalQueryForDetection.toLowerCase().includes('cc')) {
            searchAsJapanese = false;
        } else {
            const wanakanaInfoOriginal = { isJapanese: wanakana.isJapanese(originalQueryForDetection), isRomaji: wanakana.isRomaji(originalQueryForDetection) };
            if (wanakanaInfoOriginal.isJapanese) {
                searchAsJapanese = true;
            } else if (wanakanaInfoOriginal.isRomaji) {
                const lowerQuery = originalQueryForDetection.toLowerCase();
                let treatAsJapaneseRomaji;
                const strongRomajiPattern = /tsu|shi|chi|kk|tt|ss|pp|tch|dzu|j[auo]|ky[auo]|gy[auo]|sh[auo]|ch[auo]|ny[auo]|hy[auo]|my[auo]|ry[auo]|by[auo]|py[auo]|n$|n(?![aiueo])/i.test(lowerQuery);
                const nonJapaneseConsonantClusterPattern = /(mb|mp|br|bl|pl|pr|tr|dr|cr|cl|fr|fl|gr|gl|str|spl|spr|scr|pt|ct|gd|ks|ps|sth|phth|mn|gn|kn|pn|ps|rh|wr)/i.test(lowerQuery);
                const hasSpanishCaCoCu = /c[aou]/i.test(originalQueryForDetection) && !/ch/i.test(originalQueryForDetection);
                const hasK = /k/i.test(lowerQuery);
                const hasL = /l/i.test(lowerQuery);
                const hasV = /v/i.test(lowerQuery);
                const hasRR = /rr/i.test(lowerQuery);
                const hasQU = /qu/i.test(lowerQuery);
                const hasStandaloneHu = /h(?=u)/i.test(lowerQuery) && !/f(?=u)/i.test(lowerQuery) && !(/sh(?=u)|ch(?=u)|hy(?=u)|ky(?=u)|gy(?=u)|ny(?=u)|my(?=u)|ry(?=u)|by(?=u)|py(?=u)/i.test(lowerQuery));
                const hasSpanishCeCi = /c[ei]/i.test(lowerQuery) && !/ch/i.test(lowerQuery);

                if (hasL || hasV || hasRR || hasQU || hasStandaloneHu || hasSpanishCeCi) treatAsJapaneseRomaji = false;
                else if (strongRomajiPattern) treatAsJapaneseRomaji = true;
                else if (nonJapaneseConsonantClusterPattern) treatAsJapaneseRomaji = false;
                else if (hasSpanishCaCoCu && !hasK) treatAsJapaneseRomaji = false;
                else treatAsJapaneseRomaji = true;

                if (treatAsJapaneseRomaji) {
                    const hiraganaAttempt = wanakana.toHiragana(originalQueryForDetection, { customKanaMapping: { ou: 'おう', oo: 'おお', ee: 'ええ' } });
                    if (!wanakana.isKana(hiraganaAttempt)) {
                        searchAsJapanese = false;
                        queryWasConverted = false;
                    } else {
                        queryForSearch = hiraganaAttempt;
                        searchAsJapanese = true;
                        queryWasConverted = true;
                    }
                } else {
                    searchAsJapanese = false;
                }
            } else {
                searchAsJapanese = false;
            }
        }
        
        searchSuggestionContainer.style.display = 'none';

        // --- FETCH DE DATOS DESDE EL SERVIDOR ---
        console.log(`Buscando en servidor: "${queryForSearch}", como japonés: ${searchAsJapanese}`);
        let vocabResults = [];
        let kanjiResults = [];

        try {
            const params = new URLSearchParams({
                query: queryForSearch,
                searchAsJapanese: searchAsJapanese
            });
            const response = await fetch(`${SEARCH_API_URL}?${params.toString()}`);
            if (!response.ok) {
                throw new Error(`Error del servidor: ${response.statusText}`);
            }
            const serverData = await response.json();
            console.log("Datos recibidos del servidor:", serverData);
            
            if (serverData.error) {
                 throw new Error(`Error en la API: ${serverData.error}`);
            }

            // Transformar los datos planos de la DB a la estructura de objetos que espera el script
            vocabResults = serverData.vocab.map(transformDbVocabEntry);
            kanjiResults = serverData.kanji.map(transformDbKanjiEntry);

            // Añadir información de JLPT y otras propiedades a los resultados de vocabulario
            vocabResults.forEach(entry => {
                entry.jlptLevel = findJlptForTerm(entry);
                const isExactDirectKanjiMatch = wanakana.isKanji(originalQueryForDetection) && entry.KJ.some(k => k.tx === originalQueryForDetection);
                const hiraganaQuery = wanakana.toHiragana(queryForSearch);
                const katakanaQuery = wanakana.toKatakana(hiraganaQuery);
                const isExactKanaMatch = entry.KN.some(k => k.tx === hiraganaQuery || k.tx === katakanaQuery);
                entry.isExactMatch = isExactDirectKanjiMatch || isExactKanaMatch;
                entry.hasSpanishDefinition = entry.SE.some(s => s.GL.some(g => g.ln === 'es'));
            });

        } catch (error) {
            console.error("Error al realizar la búsqueda en el servidor:", error);
            vocabResultsList.innerHTML = `<p class="error-message">No se pudo completar la búsqueda. ${error.message}</p>`;
            kanjiResultsList.innerHTML = '';
            return;
        }

        // Lógica de ordenación y renderizado (sin cambios)
        vocabResults.sort(sortVocabResults);
        kanjiResults.sort(sortKanjiResults);

        currentVocabResults = vocabResults;
        currentKanjiResults = kanjiResults;
        currentVocabOffset = 0;
        currentKanjiOffset = 0;

        vocabResultsList.innerHTML = '';
        kanjiResultsList.innerHTML = '';

        renderPaginatedVocabResults();
        renderPaginatedKanjiResults();

        if (queryWasConverted && !forceNonJapaneseSearch) {
            searchInput.value = queryForSearch;
            searchOriginalQueryLink.textContent = originalQueryForDetection;
            searchSuggestionContainer.style.display = 'block';
        } else {
            searchSuggestionContainer.style.display = 'none';
        }
    }

    // El resto del archivo JS (sortVocabResults, renderPaginatedVocabResults, showView, renderVocabDetail, etc.)
    // no necesita cambios significativos porque las funciones de transformación se encargan de adaptar los datos
    // del servidor al formato que estas funciones ya esperan. Se pegará el resto del código tal cual.

    // ... (El resto del archivo JS desde sortVocabResults hasta el final se pega aquí sin cambios)
    
    function sortVocabResults(a, b) {
        if (a.hasSpanishDefinition !== b.hasSpanishDefinition) {
            return b.hasSpanishDefinition - a.hasSpanishDefinition;
        }
        if (a.isExactMatch !== b.isExactMatch) {
            return b.isExactMatch - a.isExactMatch;
        }
        const jlptOrder = { 'N5': 1, 'N4': 2, 'N3': 3, 'N2': 4, 'N1': 5 };
        const aJlpt = a.jlptLevel ? jlptOrder[a.jlptLevel] : 6;
        const bJlpt = b.jlptLevel ? jlptOrder[b.jlptLevel] : 6;
        if (aJlpt !== bJlpt) return aJlpt - bJlpt;
        const aIsCommon = a.KJ.some(k => k.co) || a.KN.some(k => k.co);
        const bIsCommon = b.KJ.some(k => k.co) || b.KN.some(k => k.co);
        if (aIsCommon !== bIsCommon) return bIsCommon - aIsCommon;
        return 0;
    }

    function sortKanjiResults(a, b) {
        const jlptOrder = { 5: 1, 4: 2, 3: 3, 2: 4, 1: 5 };
        const aJlptData = kanjiJsonData[a.l];
        const bJlptData = kanjiJsonData[b.l];
        const aJlpt = aJlptData?.jlpt_new ? jlptOrder[aJlptData.jlpt_new] : 6;
        const bJlpt = bJlptData?.jlpt_new ? jlptOrder[bJlptData.jlpt_new] : 6;
        if (aJlpt !== bJlpt) return aJlpt - bJlpt;
        const aFreq = a.ms?.fr || Infinity;
        const bFreq = b.ms?.fr || Infinity;
        return aFreq - bFreq;
    }


    // ========== RENDERIZADO DE RESULTADOS DE BÚSQUEDA (PAGINADO) ==========
    function renderPaginatedVocabResults() {
        vocabResultsTitle.textContent = `Vocabulario — (${currentVocabResults.length})`;
        const itemsToRender = currentVocabResults.slice(currentVocabOffset, currentVocabOffset + ITEMS_PER_LOAD);

        if (itemsToRender.length === 0 && currentVocabOffset === 0) {
            vocabResultsList.innerHTML = '<p class="no-results">No se encontraron palabras.</p>';
            vocabShowMoreButton.style.display = 'none';
            return;
        }

        itemsToRender.forEach(entry => renderSingleVocabItem(entry, vocabResultsList));
        currentVocabOffset += itemsToRender.length;

        vocabShowMoreButton.style.display = currentVocabOffset < currentVocabResults.length ? 'block' : 'none';
    }

    function renderPaginatedKanjiResults() {
        kanjiResultsTitle.textContent = `Kanji — (${currentKanjiResults.length})`;
        const itemsToRender = currentKanjiResults.slice(currentKanjiOffset, currentKanjiOffset + ITEMS_PER_LOAD);

        if (itemsToRender.length === 0 && currentKanjiOffset === 0) {
            kanjiResultsList.innerHTML = '<p class="no-results">No se encontraron kanjis.</p>';
            kanjiShowMoreButton.style.display = 'none';
            return;
        }

        itemsToRender.forEach(kEntry => renderSingleKanjiItem(kEntry, kanjiResultsList));
        currentKanjiOffset += itemsToRender.length;

        kanjiShowMoreButton.style.display = currentKanjiOffset < currentKanjiResults.length ? 'block' : 'none';
    }

    function renderSingleVocabItem(entry, parentListElement) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'vocab-item';
        itemDiv.dataset.vocabId = entry.id;

        const headerDiv = document.createElement('div');
        headerDiv.className = 'vocab-item-header';
        const termH3 = document.createElement('h3');
        termH3.className = 'vocab-term';
        termH3.innerHTML = generateTermDisplayHtml(entry).html;
        headerDiv.appendChild(termH3);
        itemDiv.appendChild(headerDiv);

        const tagsDiv = document.createElement('div');
        tagsDiv.className = 'vocab-tags';
        getTagsForEntry(entry).forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.className = `tag ${tag.class}`;
            tagSpan.textContent = tag.text;
            tagsDiv.appendChild(tagSpan);
        });
        itemDiv.appendChild(tagsDiv);

        const sensesOl = document.createElement('ol');
        sensesOl.className = 'vocab-senses';
        const hasAnySpanishGloss = entry.SE.some(s => s.GL.some(g => g.ln === 'es'));
        let senseDisplayNumber = 1;

        for (const sense of entry.SE) {
            let glossToDisplay = null;

            if (hasAnySpanishGloss) {
                const spanishGloss = sense.GL.find(g => g.ln === 'es');
                if (spanishGloss) {
                    glossToDisplay = spanishGloss.tx;
                }
            } else {
                const englishGloss = sense.GL.find(g => g.ln === 'en');
                if (englishGloss) {
                    glossToDisplay = englishGloss.tx;
                }
            }

            if (glossToDisplay) {
                const senseLi = document.createElement('li');
                senseLi.className = 'vocab-sense';
                const glossP = document.createElement('p');
                glossP.className = 'vocab-gloss';

                let cleanedGloss = glossToDisplay.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, '');
                glossP.innerHTML = `<strong class="sense-number-dict">${senseDisplayNumber}.</strong> <span class="sense-text-content">${cleanedGloss}</span>`;

                senseLi.appendChild(glossP);
                sensesOl.appendChild(senseLi);
                senseDisplayNumber++;
            }
        }

        if (senseDisplayNumber === 1) {
            sensesOl.innerHTML = '<li><p class="vocab-gloss"><strong class="sense-number-dict">1.</strong> <span class="sense-text-content"><em>No hay definiciones disponibles.</em></span></p></li>';
        }
        itemDiv.appendChild(sensesOl);

        const tempDiv = document.createElement('div');
        tempDiv.style.visibility = 'hidden';
        tempDiv.style.position = 'absolute';
        tempDiv.appendChild(sensesOl.cloneNode(true));
        document.body.appendChild(tempDiv);
        if (tempDiv.querySelector('ol').scrollHeight > 180) {
             sensesOl.classList.add('apply-fade');
        } else {
             sensesOl.classList.remove('apply-fade');
             sensesOl.style.maxHeight = 'none';
        }
        document.body.removeChild(tempDiv);

        itemDiv.addEventListener('click', () => showView('vocabDetail', entry.id));
        parentListElement.appendChild(itemDiv);
    }

    function renderSingleKanjiItem(kEntry, parentListElement) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'kanji-item';
        itemDiv.dataset.kanjiChar = kEntry.l;

        const jlptData = kanjiJsonData[kEntry.l];
        if (jlptData?.jlpt_new) {
            const jlptBadge = document.createElement('div');
            jlptBadge.className = 'kanji-item-jlpt-badge';
            jlptBadge.textContent = `N${jlptData.jlpt_new}`;
            itemDiv.appendChild(jlptBadge);
        }

        const charContainerDiv = document.createElement('div');
        charContainerDiv.className = 'kanji-item-char-container';
        const charSpan = document.createElement('span');
        charSpan.className = 'kanji-item-char';
        charSpan.textContent = kEntry.l;
        charContainerDiv.appendChild(charSpan);
        itemDiv.appendChild(charContainerDiv);

        const infoDiv = document.createElement('div');
        infoDiv.className = 'kanji-item-info';

        const meaningP = document.createElement('p');
        meaningP.className = 'kanji-item-meaning';
        let spanishMeaning, englishMeaning;
        if (kEntry.rm && kEntry.rm.g && kEntry.rm.g[0] && kEntry.rm.g[0].mn) {
            spanishMeaning = kEntry.rm.g[0].mn.find(m => m.ln === 'es');
            englishMeaning = kEntry.rm.g[0].mn.find(m => m.ln === 'en');
        }
        meaningP.textContent = spanishMeaning ? spanishMeaning.v : (englishMeaning ? englishMeaning.v : 'N/A');
        infoDiv.appendChild(meaningP);

        const readingsDiv = document.createElement('div');
        readingsDiv.className = 'kanji-item-readings';
        let onReadings = 'N/A';
        let kunReadings = 'N/A';

        if (kEntry.rm && kEntry.rm.g && kEntry.rm.g[0] && kEntry.rm.g[0].rd) {
            onReadings = kEntry.rm.g[0].rd.filter(r => r.t === 'ja_on').map(r => r.v).join(', ') || 'N/A';
            kunReadings = kEntry.rm.g[0].rd.filter(r => r.t === 'ja_kun').map(r => r.v).join(', ') || 'N/A';
        }

        readingsDiv.innerHTML = `<p><strong>On:</strong> ${onReadings}</p><p><strong>Kun:</strong> ${kunReadings}</p>`;
        infoDiv.appendChild(readingsDiv);

        itemDiv.appendChild(infoDiv);
        itemDiv.addEventListener('click', () => showView('kanjiDetail', kEntry.l));
        parentListElement.appendChild(itemDiv);
    }

    function clearSearchResults() {
        vocabResultsList.innerHTML = '';
        kanjiResultsList.innerHTML = '';
        vocabResultsTitle.textContent = 'Vocabulario — (0)';
        kanjiResultsTitle.textContent = 'Kanji — (0)';
        vocabShowMoreButton.style.display = 'none';
        kanjiShowMoreButton.style.display = 'none';
    }

    function handleResponsiveTabs() {
        if (window.innerWidth <= 768) {
            searchResultsTabsContainer.style.display = 'flex';
            document.getElementById('vocabulary-results-section').classList.add('active-results-section-mobile');
            document.getElementById('kanji-results-section').classList.remove('active-results-section-mobile');
            searchResultsTabButtons.forEach(btn => btn.classList.remove('active-tab'));
            searchResultsTabButtons[0].classList.add('active-tab');
        } else {
            searchResultsTabsContainer.style.display = 'none';
            document.getElementById('vocabulary-results-section').classList.remove('active-results-section-mobile');
            document.getElementById('kanji-results-section').classList.remove('active-results-section-mobile');
        }
    }

    // ========== LÓGICA DE LAS VISTAS DE DETALLE ==========
    function showView(viewName, dataId, isBackNavigation = false) {
        mainView.classList.remove('active-dict-view');
        vocabDetailView.classList.remove('active-dict-view');
        kanjiDetailView.classList.remove('active-dict-view');

        if (!isBackNavigation) {
            navigationHistory.push({ view: viewName, id: dataId });
        }

        if (viewName === 'main') {
            mainView.classList.add('active-dict-view');
        } else if (viewName === 'vocabDetail') {
            currentDetailTerm = allFoundVocabDetails[dataId]; // Usar el cache
            if (currentDetailTerm) renderVocabDetail(currentDetailTerm);
            if (!isBackNavigation) {
                window.location.hash = 'type=wordJmdict&id=' + encodeURIComponent(dataId);
            }
            vocabDetailView.classList.add('active-dict-view');
        } else if (viewName === 'kanjiDetail') {
            currentDetailKanji = allFoundKanjiDetails[dataId]; // Usar el cache
            if (currentDetailKanji) {
                renderKanjiDetail(currentDetailKanji);
            }
            if (!isBackNavigation) {
                window.location.hash = 'type=kanjiKanjidic&id=' + encodeURIComponent(dataId);
            }
            kanjiDetailView.classList.add('active-dict-view');
        }
        window.scrollTo(0, 0);
    }

    function goBack() {
        if (navigationHistory.length <= 1) {
            navigationHistory = [];
            showView('main', null, false);
            return;
        }
        navigationHistory.pop();
        const previousState = navigationHistory[navigationHistory.length - 1];
        if (previousState) {
            showView(previousState.view, previousState.id, true);
        } else {
            showView('main', null, false);
        }
    }

    async function renderVocabDetail(entry) { // Convertida a async para futura carga de datos si es necesario
        const termDisplayResult = generateTermDisplayHtml(entry);
        vocabDetailTermDisplay.innerHTML = `<h3 class="vocab-term">${termDisplayResult.html}</h3>`;

        const existingPocoComunDiv = vocabDetailTermDisplayWrapper.querySelector('.poco-comun-kanji-dict');
        if (existingPocoComunDiv) {
            existingPocoComunDiv.remove();
        }

        if (termDisplayResult.otherForms && termDisplayResult.otherForms.length > 0) {
            const pocoComunDiv = document.createElement('div');
            pocoComunDiv.className = 'poco-comun-kanji-dict';
            pocoComunDiv.innerHTML = `Poco común: ${termDisplayResult.otherForms.join('、')}`;
            vocabDetailTermDisplay.insertAdjacentElement('afterend', pocoComunDiv);
        }

        vocabDetailTagsContainer.innerHTML = '';
        getTagsForEntry(entry).forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.className = `tag ${tag.class}`;
            tagSpan.textContent = tag.text;
            vocabDetailTagsContainer.appendChild(tagSpan);
        });

        vocabDetailSensesList.innerHTML = '';
        let spanishSensesFound = false;
        let senseNumberDetail = 1;
        entry.SE.forEach(sense => {
            const spanishGlossObjects = sense.GL.filter(g => g.ln === 'es');
            if (spanishGlossObjects.length > 0) {
                const spanishGlossText = spanishGlossObjects.map(g => g.tx).join('; ');
                const senseDiv = document.createElement('div');
                senseDiv.className = 'vocab-detail-sense-item';
                const glossP = document.createElement('p');
                glossP.className = 'vocab-gloss';

                let tagHtml = '';
                if (sense.ms && sense.ms.includes('uk')) {
                    const ukTagSpan = document.createElement('span');
                    ukTagSpan.className = 'tag tag-kana-only sense-uk-tag';
                    ukTagSpan.textContent = 'Kana';
                    tagHtml = ukTagSpan.outerHTML + ' ';
                }

                let senseInfoText = '';
                if (sense.in && sense.in.length > 0) {
                    senseInfoText = ` <span class="sense-info-dict">(${sense.in.join('; ')})</span>`;
                }

                let senseFdText = '';
                if (sense.fd && sense.fd.length > 0) {
                    senseFdText = ` <span class="sense-fd-dict">[${sense.fd.join('; ')}]</span>`;
                }

                const cleanedGloss = spanishGlossText.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, '');
                glossP.innerHTML = `<strong class="sense-number-dict">${senseNumberDetail}.</strong> <span class="sense-text-content">${tagHtml}${cleanedGloss}${senseInfoText}${senseFdText}</span>`;

                senseDiv.appendChild(glossP);
                vocabDetailSensesList.appendChild(senseDiv);
                spanishSensesFound = true;
                senseNumberDetail++;
            }
        });

        vocabDetailEnglishSensesList.innerHTML = '';
        let englishSensesFound = false;
        let englishSenseNumberDetail = 1;
        entry.SE.forEach(sense => {
            const englishGlossObjects = sense.GL.filter(g => g.ln === 'en');
            if (englishGlossObjects.length > 0) {
                const englishGlossText = englishGlossObjects.map(g => g.tx).join('; ');
                const senseDiv = document.createElement('div');
                senseDiv.className = 'vocab-detail-sense-item';
                const glossP = document.createElement('p');
                glossP.className = 'vocab-gloss';

                let tagHtmlEng = '';
                if (sense.ms && sense.ms.includes('uk')) {
                    const ukTagSpan = document.createElement('span');
                    ukTagSpan.className = 'tag tag-kana-only sense-uk-tag';
                    ukTagSpan.textContent = 'Kana';
                    tagHtmlEng = ukTagSpan.outerHTML + ' ';
                }

                let senseInfoTextEng = '';
                if (sense.in && sense.in.length > 0) {
                    senseInfoTextEng = ` <span class="sense-info-dict">(${sense.in.join('; ')})</span>`;
                }

                let senseFdTextEng = '';
                if (sense.fd && sense.fd.length > 0) {
                    senseFdTextEng = ` <span class="sense-fd-dict">[${sense.fd.join('; ')}]</span>`;
                }

                const cleanedGlossEng = englishGlossText.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, '');
                glossP.innerHTML = `<strong class="sense-number-dict">${englishSenseNumberDetail}.</strong> <span class="sense-text-content">${tagHtmlEng}${cleanedGlossEng}${senseInfoTextEng}${senseFdTextEng}</span>`;

                senseDiv.appendChild(glossP);

                if (spanishSensesFound) {
                    vocabDetailEnglishSensesList.appendChild(senseDiv);
                } else {
                    vocabDetailSensesList.appendChild(senseDiv);
                }
                englishSensesFound = true;
                englishSenseNumberDetail++;
            }
        });

        if (spanishSensesFound) {
            vocabDetailSensesTitle.textContent = 'Definiciones en Español';
            vocabDetailSensesContainer.style.display = 'block';
            if (englishSensesFound) {
                vocabDetailEnglishSensesContainer.style.display = 'block';
            } else {
                vocabDetailEnglishSensesContainer.style.display = 'none';
            }
        } else if (englishSensesFound) {
            vocabDetailSensesTitle.textContent = 'Definiciones en Inglés';
            vocabDetailSensesContainer.style.display = 'block';
            vocabDetailEnglishSensesContainer.style.display = 'none';
        } else {
            vocabDetailSensesTitle.textContent = 'Definiciones';
            vocabDetailSensesList.innerHTML = '<p><strong class="sense-number-dict">1.</strong> <span class="sense-text-content"><em>No hay definiciones disponibles.</em></span></p>';
            vocabDetailSensesContainer.style.display = 'block';
            vocabDetailEnglishSensesContainer.style.display = 'none';
        }

        vocabDetailKanjiGrid.innerHTML = '';
        const uniqueKanjisInTerm = new Set();
        if (entry.KJ[0]?.tx) {
            entry.KJ[0].tx.split('').forEach(char => {
                if (wanakana.isKanji(char)) uniqueKanjisInTerm.add(char);
            });
        }
        if (termDisplayResult.otherForms) {
            termDisplayResult.otherForms.forEach(form => {
                form.split('').forEach(char => {
                    if (wanakana.isKanji(char)) uniqueKanjisInTerm.add(char);
                });
            });
        }

        uniqueKanjisInTerm.forEach(kChar => {
            const kanjiDiv = document.createElement('div');
            kanjiDiv.className = 'kanji-item-char';
            kanjiDiv.textContent = kChar;
            kanjiDiv.addEventListener('click', () => showView('kanjiDetail', kChar));
            vocabDetailKanjiGrid.appendChild(kanjiDiv);
        });

        const displayTermForModalAndActions = entry.KJ[0]?.tx || entry.KN[0]?.tx;

        currentExampleOffset = 0;
        vocabDetailExamplesList.innerHTML = '';

        const primarySearchTerms = [];
        const mainKanjiFormForExamples = entry.KJ[0]?.tx;
        const primaryKanaObjForExamples = entry.KN.find(k => k.co) || entry.KN[0];
        const mainKanaFormForExamples = primaryKanaObjForExamples?.tx;
        const isUsuallyKanaForExamples = shouldDisplayAsUsuallyKana(entry);

        if (mainKanjiFormForExamples && !isUsuallyKanaForExamples) {
            primarySearchTerms.push(mainKanjiFormForExamples);
        } else {
            if (mainKanjiFormForExamples) {
                primarySearchTerms.push(mainKanjiFormForExamples);
            }
            if (mainKanaFormForExamples) {
                if (!primarySearchTerms.includes(mainKanaFormForExamples)) {
                    primarySearchTerms.push(mainKanaFormForExamples);
                }
            }
        }
        if (primarySearchTerms.length === 0 && mainKanaFormForExamples) {
             primarySearchTerms.push(mainKanaFormForExamples);
        }
        if (primarySearchTerms.length === 0 && mainKanjiFormForExamples) {
            primarySearchTerms.push(mainKanjiFormForExamples);
        }

        currentExampleSentences = [];
        if (primarySearchTerms.length > 0) {
            let filteredExamples = tatoebaSentences.filter(s => {
                if (!s || !s.jp) return false;
                return primarySearchTerms.some(term => s.jp.includes(term));
            });
            currentExampleSentences = Array.from(new Map(filteredExamples.map(ex => [ex.jp, ex])).values());
        }

        if (vocabDetailExamplesTitle) {
            vocabDetailExamplesTitle.textContent = `Oraciones de Ejemplo (${currentExampleSentences.length})`;
        }
        renderPaginatedExamples();


        currentItemForModal = { type: 'vocab', id: entry.id, name: displayTermForModalAndActions };
        updateVocabLikeButtonVisual();
    }

    function renderPaginatedExamples() {
        const itemsToRender = currentExampleSentences.slice(currentExampleOffset, currentExampleOffset + ITEMS_PER_LOAD);

        if (itemsToRender.length === 0 && currentExampleOffset === 0) {
            vocabDetailExamplesList.innerHTML = '<p><em>No hay ejemplos disponibles.</em></p>';
            vocabExamplesShowMoreButton.style.display = 'none';
            return;
        }

        let termsToHighlightInExample = [];
        if (currentDetailTerm) {
            const mainKanjiFormHighlight = currentDetailTerm.KJ[0]?.tx;
            const primaryKanaObjHighlight = currentDetailTerm.KN.find(k => k.co) || currentDetailTerm.KN[0];
            const mainKanaFormHighlight = primaryKanaObjHighlight?.tx;
            const isUsuallyKanaHighlight = shouldDisplayAsUsuallyKana(currentDetailTerm);

            if (mainKanjiFormHighlight && !isUsuallyKanaHighlight) {
                termsToHighlightInExample.push(mainKanjiFormHighlight);
            } else {
                if (mainKanjiFormHighlight) {
                    termsToHighlightInExample.push(mainKanjiFormHighlight);
                }
                if (mainKanaFormHighlight) {
                    if (!termsToHighlightInExample.includes(mainKanaFormHighlight)) {
                        termsToHighlightInExample.push(mainKanaFormHighlight);
                    }
                }
                if (termsToHighlightInExample.length === 0) {
                    const anyKanji = currentDetailTerm.KJ.find(k => k.tx)?.tx;
                    const anyKana = currentDetailTerm.KN.find(k => k.tx)?.tx;
                    if (anyKanji) termsToHighlightInExample.push(anyKanji);
                    if (anyKana && !termsToHighlightInExample.includes(anyKana)) termsToHighlightInExample.push(anyKana);
                }
            }
            termsToHighlightInExample = [...new Set(termsToHighlightInExample.filter(Boolean))].sort((a, b) => b.length - a.length);
        }

        itemsToRender.forEach(ex => {
            const exDiv = document.createElement('div');
            exDiv.className = 'sense-example';
            let highlightedJp = ex.jp;
            termsToHighlightInExample.forEach(termToHighlight => {
                if (termToHighlight) {
                    const escapedTerm = termToHighlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(escapedTerm, 'g');
                    highlightedJp = highlightedJp.replace(regex, `<strong>$&</strong>`);
                }
            });
            exDiv.innerHTML = `<p class="example-jp">${highlightedJp}</p><p class="example-es">${ex.es}</p>`;
            vocabDetailExamplesList.appendChild(exDiv);
        });
        currentExampleOffset += itemsToRender.length;

        vocabExamplesShowMoreButton.style.display = currentExampleOffset < currentExampleSentences.length ? 'block' : 'none';
    }

    async function renderKanjiDetail(kEntry) { // Convertida a async
        if (!kEntry || !kEntry.l) return;
        currentDetailKanji = kEntry;
        kanjiDetailCharForData.textContent = kEntry.l;

        const spanishMeaningHeader = kEntry.rm?.g[0]?.mn.find(m => m.ln === 'es');
        const englishMeaningHeader = kEntry.rm?.g[0]?.mn.find(m => m.ln === 'en');
        kanjiDetailMainMeaningHeader.textContent = spanishMeaningHeader ? spanishMeaningHeader.v : (englishMeaningHeader ? englishMeaningHeader.v : '');

        initDmakDict(kEntry.l);

        const kjJson = kanjiJsonData[kEntry.l] || {};
        kanjiDetailJlpt.textContent = kjJson.jlpt_new ? `N${kjJson.jlpt_new}` : 'N/A';
        kanjiDetailFrequency.textContent = kEntry.ms?.fr || 'N/A';
        kanjiDetailStrokes.textContent = kEntry.ms?.sc[0] || 'N/A';

        const onReadings = kEntry.rm?.g.flatMap(group => group.rd.filter(r => r.t === 'ja_on').map(r => r.v)).join(', ') || 'N/A';
        const kunReadings = kEntry.rm?.g.flatMap(group => group.rd.filter(r => r.t === 'ja_kun').map(r => r.v)).join(', ') || 'N/A';
        kanjiDetailOnYomi.textContent = onReadings;
        kanjiDetailKunYomi.textContent = kunReadings;

        kanjiMeaningsTab.innerHTML = '';
        let meaningNumber = 1;
        let meaningsFound = false;
        kEntry.rm?.g.forEach(group => {
            group.mn.forEach(m => {
                if (m.ln === 'es' || (m.ln === 'en' && !group.mn.some(mx => mx.ln === 'es'))) {
                    const p = document.createElement('p');
                    const cleanedMeaning = m.v.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, '');
                    p.innerHTML = `<strong>${meaningNumber}.</strong> <span class="sense-text-content">${cleanedMeaning}${m.ln === 'en' ? ' (en)' : ''}</span>`;
                    kanjiMeaningsTab.appendChild(p);
                    meaningNumber++;
                    meaningsFound = true;
                }
            });
        });
        if (!meaningsFound) {
            kanjiMeaningsTab.innerHTML = '<p><em>No hay significados disponibles.</em></p>';
        }

        kanjiRadicalsTab.innerHTML = '';
        const components = kradfileData[kEntry.l] || [];
        if (components.length > 0) {
            components.forEach(compSymbol => {
                if (compSymbol === kEntry.l) return;
                const compSpan = document.createElement('span');
                compSpan.className = 'krad-component-link kanji-char';
                compSpan.textContent = compSymbol;
                compSpan.title = `Ver detalle de ${compSymbol}`;
                compSpan.addEventListener('click', () => {
                     // Comprobamos si el componente existe en nuestro cache de kanjis, si no, lo buscamos
                    if (allFoundKanjiDetails[compSymbol]) {
                        showView('kanjiDetail', compSymbol);
                    } else {
                        // Si no está, lo buscamos en el servidor y luego mostramos la vista
                        performSearch(compSymbol).then(() => {
                            if (allFoundKanjiDetails[compSymbol]) {
                                showView('kanjiDetail', compSymbol);
                            }
                        });
                    }
                });
                kanjiRadicalsTab.appendChild(compSpan);
                kanjiRadicalsTab.appendChild(document.createTextNode(' '));
            });
        } else {
            kanjiRadicalsTab.innerHTML = '<p><em>No hay datos de componentes.</em></p>';
        }

        kanjiUserNotesTextarea.value = userProgress.kanjiUserNotes[kEntry.l] || '';
        kanjiUserNotesTextarea.readOnly = true;
        kanjiSaveNotesButton.classList.add('hidden');
        kanjiEditNotesButton.classList.remove('hidden');
        kanjiResetNotesButton.disabled = !userProgress.kanjiUserNotes[kEntry.l];

        // Lógica para palabras relacionadas ahora necesita hacer una nueva búsqueda
        currentKanjiCommonWordsOffset = 0;
        currentKanjiAllWordsOffset = 0;
        kanjiCommonWordsList.innerHTML = 'Cargando palabras...';
        kanjiAllWordsList.innerHTML = 'Cargando palabras...';

        // Hacer una búsqueda específica para el kanji y rellenar las listas de palabras
        try {
            const params = new URLSearchParams({ query: kEntry.l, searchAsJapanese: true });
            const response = await fetch(`${SEARCH_API_URL}?${params.toString()}`);
            const relatedWordsData = await response.json();
            
            console.log("Palabras relacionadas para " + kEntry.l, relatedWordsData);

            const allWords = relatedWordsData.vocab.map(transformDbVocabEntry);
            allWords.forEach(e => { e.jlptLevel = findJlptForTerm(e); });

            currentKanjiAllWords = allWords.sort(sortVocabResults);
            currentKanjiCommonWords = allWords.filter(e =>
                e.KJ.some(k => k.co) || e.KN.some(k => k.co)
            ).sort(sortVocabResults);
            
            kanjiCommonWordsList.innerHTML = '';
            kanjiAllWordsList.innerHTML = '';
            renderPaginatedKanjiRelatedWords('common');
            renderPaginatedKanjiRelatedWords('all');

        } catch (error) {
            console.error("Error cargando palabras relacionadas: ", error);
            kanjiCommonWordsList.innerHTML = '<p class="no-results">Error al cargar palabras.</p>';
            kanjiAllWordsList.innerHTML = '<p class="no-results">Error al cargar palabras.</p>';
        }


        currentItemForModal = { type: 'kanji', id: kEntry.l, name: kEntry.l };
        updateKanjiLikeButtonVisual();
    }

    function renderPaginatedKanjiRelatedWords(type) {
        let listElement, wordsArray, offset, showMoreButton;

        if (type === 'common') {
            listElement = kanjiCommonWordsList;
            wordsArray = currentKanjiCommonWords;
            offset = currentKanjiCommonWordsOffset;
            showMoreButton = kanjiCommonWordsShowMoreButton;
        } else {
            listElement = kanjiAllWordsList;
            wordsArray = currentKanjiAllWords;
            offset = currentKanjiAllWordsOffset;
            showMoreButton = kanjiAllWordsShowMoreButton;
        }

        const itemsToRender = wordsArray.slice(offset, offset + ITEMS_PER_LOAD);

        if (itemsToRender.length === 0 && offset === 0) {
            listElement.innerHTML = '<p class="no-results"><em>No se encontraron palabras.</em></p>';
            showMoreButton.style.display = 'none';
            return;
        }

        itemsToRender.forEach(entry => {
            renderSingleVocabItem(entry, listElement);
        });

        if (type === 'common') {
            currentKanjiCommonWordsOffset += itemsToRender.length;
            showMoreButton.style.display = currentKanjiCommonWordsOffset < wordsArray.length ? 'block' : 'none';
        } else {
            currentKanjiAllWordsOffset += itemsToRender.length;
            showMoreButton.style.display = currentKanjiAllWordsOffset < wordsArray.length ? 'block' : 'none';
        }
    }

    // ========== FUNCIONES AUXILIARES (Furigana, Tags, Dmak) (sin cambios) ==========

    function shouldDisplayAsUsuallyKana(entry) {
        const firstSense = entry.SE?.[0];
        if (!firstSense) return false;
        return firstSense.ms?.includes('uk') || false;
    }

    function generateTermDisplayHtml(entry) {
        const primaryKanaObj = entry.KN.find(k => k.co) || entry.KN[0];
        const primaryKanaText = primaryKanaObj?.tx;
        const kanjiInitiallyFilteredByIdenticalKanaExclusion = (entry.KJ || [])
            .filter(k_ele => k_ele.tx && k_ele.tx !== primaryKanaText);
        const SEARCH_ONLY_KANJI_TAG = "sK";
        const kanjiForDisplayConsideration = kanjiInitiallyFilteredByIdenticalKanaExclusion
            .filter(k_ele => !(k_ele.tg && k_ele.tg.includes(SEARCH_ONLY_KANJI_TAG)));
        const rKForms = kanjiForDisplayConsideration.filter(k_ele => k_ele.tg && k_ele.tg.includes("rK"));
        const nonRKForms = kanjiForDisplayConsideration.filter(k_ele => !(k_ele.tg && k_ele.tg.includes("rK")));
        const displayAsUk = shouldDisplayAsUsuallyKana(entry);
        let mainDisplayKanjiElements = [];
        if (nonRKForms.length > 0) {
            mainDisplayKanjiElements = nonRKForms.filter(k_ele =>
                k_ele.co && !(k_ele.tg && k_ele.tg.includes(USUALLY_KANA_WRITING_TAG))
            );
            if (mainDisplayKanjiElements.length === 0) {
                mainDisplayKanjiElements = nonRKForms.filter(k_ele => k_ele.co);
            }
            if (mainDisplayKanjiElements.length === 0) {
                mainDisplayKanjiElements = nonRKForms.filter(k_ele =>
                    !(k_ele.tg && k_ele.tg.includes(USUALLY_KANA_WRITING_TAG))
                );
            }
            if (mainDisplayKanjiElements.length === 0 && nonRKForms.length > 0) {
                 mainDisplayKanjiElements = nonRKForms;
            }
        }
        let otherKanjiTextsToDisplaySeparately = nonRKForms
            .filter(k_ele => !mainDisplayKanjiElements.find(main_k => main_k.tx === k_ele.tx))
            .map(k_ele => k_ele.tx);
        otherKanjiTextsToDisplaySeparately = otherKanjiTextsToDisplaySeparately.concat(rKForms.map(k_ele => k_ele.tx));
        let mainDisplayHtml = "";
        if (displayAsUk) {
            mainDisplayHtml = `<span class="kana-char">${primaryKanaText || ''}</span>`;
            let parentheticalKanjiTexts = mainDisplayKanjiElements.map(k => k.tx);
            if (parentheticalKanjiTexts.length > 0) {
                mainDisplayHtml += ` <span class="kanji-in-parens">(${[...new Set(parentheticalKanjiTexts)].join('、')})</span>`;
            }
        } else {
            if (mainDisplayKanjiElements.length === 0) {
                mainDisplayHtml = `<span class="kana-char">${primaryKanaText || (entry.KJ[0]?.tx || '')}</span>`;
            } else if (mainDisplayKanjiElements.length === 1) {
                mainDisplayHtml = generateSingleFuriganaHtml(mainDisplayKanjiElements[0].tx, primaryKanaText);
            } else {
                let furiganaParts = mainDisplayKanjiElements.map(k_ele =>
                    generateSingleFuriganaHtml(k_ele.tx, primaryKanaText)
                );
                mainDisplayHtml = [...new Set(furiganaParts)].join('、');
            }
        }
        return {
            html: mainDisplayHtml || `<span class="kana-char">${primaryKanaText || ''}</span>`,
            otherForms: [...new Set(otherKanjiTextsToDisplaySeparately)].filter(Boolean)
        };
    }

    function generateSingleFuriganaHtml(kanjiStringToParse, fullKanaReading) {
        let html = '';
        if (!fullKanaReading || kanjiStringToParse === fullKanaReading || wanakana.isKana(kanjiStringToParse)) {
            return `<span class="kanji-char">${kanjiStringToParse}</span>`;
        }
        const kanjiTokens = wanakana.tokenize(kanjiStringToParse, { detailed: true });
        const hiraganaReading = wanakana.toHiragana(fullKanaReading);
        let readingIdx = 0;
        for (let i = 0; i < kanjiTokens.length; i++) {
            const token = kanjiTokens[i];
            if (token.type === 'kana' || token.type === 'hiragana' || token.type === 'katakana') {
                html += `<span class="kana-char">${token.value}</span>`;
                const lengthToAdvance = wanakana.toHiragana(token.value).length;
                readingIdx += lengthToAdvance;
            } else if (token.type === 'japanesePunctuation' && token.value === 'ー') {
                html += token.value;
                readingIdx += 1;
            } else if (token.type === 'kanji') {
                let furigana = '';
                const currentKanjiChar = token.value;
                const remainingReading = hiraganaReading.substring(readingIdx);
                let nextKanaTokenIndex = -1;
                let nextKanjiTokenIndex = -1;
                for (let j = i + 1; j < kanjiTokens.length; j++) {
                    if (kanjiTokens[j].type === 'kana' || kanjiTokens[j].type === 'hiragana' || kanjiTokens[j].type === 'katakana') {
                        nextKanaTokenIndex = j;
                        break;
                    }
                    if (kanjiTokens[j].type === 'kanji') {
                         nextKanjiTokenIndex = j;
                         break;
                    }
                }
                if (nextKanaTokenIndex !== -1) {
                    const nextKanaOkurigana = kanjiTokens[nextKanaTokenIndex].value;
                    const nextKanaHiragana = wanakana.toHiragana(nextKanaOkurigana);

                    let okuriganaPosInReading = -1;
                    const lastOccurrence = remainingReading.lastIndexOf(nextKanaHiragana);
                    if (lastOccurrence !== -1) {
                        if (lastOccurrence > 0) {
                            okuriganaPosInReading = lastOccurrence;
                        } else {
                            const firstOccurrence = remainingReading.indexOf(nextKanaHiragana);
                            if (firstOccurrence !== lastOccurrence) {
                                okuriganaPosInReading = remainingReading.indexOf(nextKanaHiragana, firstOccurrence + 1);
                            }
                        }
                    }

                    if (okuriganaPosInReading !== -1) {
                        furigana = remainingReading.substring(0, okuriganaPosInReading);
                    } else {
                        furigana = remainingReading.substring(0, currentKanjiChar.length);
                    }
                } else {
                    if (nextKanjiTokenIndex === -1) {
                        furigana = remainingReading;
                    } else {
                         furigana = remainingReading.substring(0, currentKanjiChar.length);
                    }
                }
                if (wanakana.isKanji(furigana) && furigana === currentKanjiChar) {
                    html += currentKanjiChar;
                } else {
                    html += `<ruby>${currentKanjiChar}<rt>${furigana || currentKanjiChar}</rt></ruby>`;
                }
                readingIdx += furigana.length;
            } else {
                html += token.value;
            }
        }
        if (!html.includes('<ruby>')) {
            return `<span class="kanji-char">${kanjiStringToParse}</span>`;
        }
        return html.replace(/<rt><\/rt>/g, '');
    }

    function getTagsForEntry(entry) {
        const tags = new Set();
        const tagDetails = [];
        if (entry.jlptLevel) {
            tagDetails.push({ text: entry.jlptLevel, class: 'tag-jlpt' });
        }
        const displayAsUkForTag = shouldDisplayAsUsuallyKana(entry);
        if (displayAsUkForTag) {
            if (!tagDetails.some(t => t.text === 'Kana')) {
                tagDetails.push({ text: 'Kana', class: 'tag-kana-only' });
            }
        }
        if (entry.KJ.some(k => k.co) || entry.KN.some(k => k.co)) {
            tagDetails.push({ text: 'Común', class: 'tag-common' });
        }
        entry.SE.forEach(s => {
            s.pS?.forEach(pos => {
                const simplePos = simplifyPos(pos);
                if (simplePos && !tags.has(simplePos.text)) {
                    tags.add(simplePos.text);
                    tagDetails.push(simplePos);
                }
            });
            s.ms?.forEach(m => {
                const simpleMisc = simplifyMisc(m);
                if (simpleMisc && !tags.has(simpleMisc.text)) {
                    tags.add(simpleMisc.text);
                    tagDetails.push(simpleMisc);
                }
            });
        });
        return tagDetails;
    }

    function simplifyPos(posCode) {
        if (posCode.startsWith('n')) return { text: 'Sustantivo', class: 'tag-noun' };
        if (posCode === 'v1' || posCode === 'v1-s') return { text: 'Verbo Ichidan', class: 'tag-verb' };
        if (posCode.startsWith('v5')) return { text: 'Verbo Godan', class: 'tag-verb' };
        if (posCode === 'vs' || posCode === 'vs-s' || posCode === 'vs-i') return { text: 'Verbo Suru', class: 'tag-verb' };
        if (posCode === 'vk') return { text: 'Verbo Kuru', class: 'tag-verb' };
        if (posCode === 'adj-i') return { text: 'Adjetivo-i', class: 'tag-adjective' };
        if (posCode === 'adj-na') return { text: 'Adjetivo-na', class: 'tag-adjective' };
        if (posCode === 'adj-no') return { text: 'Sust. + No (Adj.)', class: 'tag-adjective' };
        if (posCode === 'adj-pn') return { text: 'Pre-Sust. Adjetival', class: 'tag-adjective' };
        if (posCode === 'adj-t' || posCode === 'adj-f') return { text: 'Adjetivo (Otro)', class: 'tag-adjective' };
        if (posCode === 'adv' || posCode === 'adv-to') return { text: 'Adverbio', class: 'tag-adverb' };
        if (posCode === 'prt') return { text: 'Partícula', class: 'tag-particle' };
        if (posCode === 'conj') return { text: 'Conjunción', class: 'tag-conjunction' };
        if (posCode === 'int') return { text: 'Interjección', class: 'tag-interjection' };
        if (posCode === 'pn') return { text: 'Pronombre', class: 'tag-pronoun' };
        if (posCode.startsWith('aux')) return { text: 'Auxiliar', class: 'tag-auxiliary' };
        if (posCode === 'ctr') return { text: 'Contador', class: 'tag-global' };
        if (posCode === 'exp') return { text: 'Expresión', class: 'tag-expression' };
        if (posCode === 'num') return { text: 'Numérico', class: 'tag-numeric' };
        if (posCode === 'unc') return { text: 'Sin clasificar', class: 'tag-global' };
        if (posCode.startsWith('pref')) return { text: 'Prefijo', class: 'tag-global' };
        if (posCode.startsWith('suf')) return { text: 'Sufijo', class: 'tag-global' };
        return null;
    }

    function simplifyMisc(miscCode) {
        if (miscCode === 'abbr') return { text: 'Abreviación', class: 'tag-global' };
        if (miscCode === 'ateji') return { text: 'Ateji', class: 'tag-global' };
        if (miscCode === 'yoji') return { text: 'Yojijukugo', class: 'tag-global' };
        if (miscCode.includes('-ben')) return { text: `Dialecto ${miscCode.replace('-ben','')}`, class: 'tag-global'};
        if (miscCode === 'chn') return { text: 'Infantil', class: 'tag-global' };
        if (miscCode === 'col') return { text: 'Coloquial', class: 'tag-global' };
        if (miscCode === 'derog') return { text: 'Derogatorio', class: 'tag-global' };
        if (miscCode === 'fam') return { text: 'Familiar', class: 'tag-global' };
        if (miscCode === 'fem') return { text: 'Femenino', class: 'tag-global' };
        if (miscCode === 'hon') return { text: 'Honorífico', class: 'tag-global' };
        if (miscCode === 'hum') return { text: 'Humilde', class: 'tag-global' };
        if (miscCode === 'id') return { text: 'Modismo', class: 'tag-global' };
        if (miscCode === 'joc') return { text: 'Jocoso', class: 'tag-global' };
        if (miscCode === 'on-mim') return { text: 'Onomatopeya', class: 'tag-global' };
        if (miscCode === 'male') return { text: 'Masculino', class: 'tag-global' };
        if (miscCode === 'obs') return { text: 'Obsoleto', class: 'tag-global' };
        if (miscCode === 'poet') return { text: 'Poético', class: 'tag-global' };
        if (miscCode === 'pol') return { text: 'Formal', class: 'tag-global' };
        if (miscCode === 'sl') return { text: 'Argot', class: 'tag-global' };
        if (miscCode === 'vulg') return { text: 'Vulgar', class: 'tag-global' };
        return null;
    }

    function initDmakDict(kanjiCharacter) {
        dmakIsAnimatingOnClickDict = false;
        if (dmakInstanceDict) {
            if (dmakInstanceDict.timeouts) {
                for (const type in dmakInstanceDict.timeouts) {
                    if (Array.isArray(dmakInstanceDict.timeouts[type])) {
                        dmakInstanceDict.timeouts[type].forEach(clearTimeout);
                    }
                    dmakInstanceDict.timeouts[type] = [];
                }
            }
            dmakInstanceDict.erase();
        }
        kanjiDetailAnimationTarget.innerHTML = '';
        const strokeColor = userProgress.theme === 'dark' ? '#FFFFFF' : '#333333';
        let dmakIsReady = false;
        let dmakAttempted = false;
        try {
            dmakInstanceDict = new Dmak(kanjiCharacter, {
                uri: '../data/kanjivg/kanji/',
                skipLoad: false, autoplay: false, step: 0.015,
                height: 200, width: 200,
                element: "kanji-detail-animation-target-dict",
                stroke: { animated: { drawing: false, erasing: false }, order: { visible: false }, attr: { "stroke": strokeColor, "stroke-width": 4 } },
                grid: { show: false },
                loaded: function(strokes) {
                    dmakAttempted = true;
                    if (strokes && strokes.length > 0) dmakIsReady = true;
                },
                drew: function(index) {
                    if (!dmakInstanceDict || !dmakInstanceDict.strokes) return;
                    if (dmakIsAnimatingOnClickDict && index === (dmakInstanceDict.strokes.length - 1)) {
                        dmakIsAnimatingOnClickDict = false;
                        setTimeout(() => {
                            const paths = kanjiDetailAnimationTarget.querySelectorAll('svg path');
                            paths.forEach(p => p.setAttribute('stroke', strokeColor));
                        }, 50);
                    }
                }
            });
        } catch (error) {
            dmakAttempted = true; dmakIsReady = false;
        }
        setTimeout(() => {
            if (dmakInstanceDict && dmakInstanceDict.text === kanjiCharacter && dmakIsReady && !dmakIsAnimatingOnClickDict) {
                dmakInstanceDict.render();
                const paths = kanjiDetailAnimationTarget.querySelectorAll('svg path');
                paths.forEach(p => p.setAttribute('stroke', strokeColor));
                kanjiDetailAnimationTarget.onclick = () => {
                    if (dmakInstanceDict && dmakIsReady) {
                        if (dmakIsAnimatingOnClickDict) {
                            if (dmakInstanceDict.timeouts && dmakInstanceDict.timeouts.play) {
                                dmakInstanceDict.timeouts.play.forEach(clearTimeout);
                                dmakInstanceDict.timeouts.play = [];
                            }
                        }
                        dmakIsAnimatingOnClickDict = true;
                        dmakInstanceDict.erase();
                        dmakInstanceDict.options.stroke.animated.drawing = true;
                        dmakInstanceDict.render();
                    }
                };
            } else if (dmakAttempted && !dmakIsReady) {
                kanjiDetailAnimationTarget.innerHTML = `<div class="kanji-animation-fallback-text-dict kanji-char">${kanjiCharacter}</div>`;
                kanjiDetailAnimationTarget.onclick = null;
            }
            else if (!dmakInstanceDict && dmakAttempted) {
                 kanjiDetailAnimationTarget.innerHTML = `<div class="kanji-animation-fallback-text-dict kanji-char">${kanjiCharacter}</div>`;
                 kanjiDetailAnimationTarget.onclick = null;
            }
        }, 150);
    }

    // ========== MANEJO DE COLECCIONES (sin cambios) ==========

    function openModalForCollection(type, itemId, itemName) {
        if (!itemId || !itemName) {
            addToCollectionFeedback.textContent = "Error: Información del item incompleta.";
            return;
        }
        currentItemForModal = { type: type, id: itemId, name: itemName };
        currentCollectionTypeForModal = type;
        modalCollectionTitle.textContent = `Añadir ${type === 'vocab' ? 'Palabra' : 'Kanji'} a Vocabulario`;
        populateExistingCollectionsInModalDict();
        newCollectionNameModalInput.value = '';
        addToCollectionFeedback.textContent = '';
        addToCollectionModal.classList.remove('hidden');
    }

    function populateExistingCollectionsInModalDict() {
        existingCollectionsListModal.innerHTML = '';
        const collections = userProgress.dictionaryCollections[currentCollectionTypeForModal] || [];

        const filteredCollections = collections.filter(collection => !collection.isSystem);

        filteredCollections.forEach(collection => {
            const div = document.createElement('div');
            div.className = 'collection-radio-item';
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `dict-coll-modal-chk-${collection.name.replace(/\s+/g, '-')}-${currentCollectionTypeForModal}`;
            checkbox.value = collection.name;
            checkbox.checked = currentItemForModal && collection.items.includes(currentItemForModal.id);
            const label = document.createElement('label');
            label.htmlFor = checkbox.id;
            label.textContent = `${collection.name} (${collection.items.length})`;
            checkbox.addEventListener('change', (e) => {
                toggleItemInExistingCollectionDict(collection.name, e.target.checked);
            });
            div.appendChild(checkbox);
            div.appendChild(label);
            existingCollectionsListModal.appendChild(div);
        });
    }

    function toggleItemInExistingCollectionDict(collectionName, isChecked) {
        const collection = userProgress.dictionaryCollections[currentCollectionTypeForModal].find(c => c.name === collectionName);
        if (collection && currentItemForModal) {
            const itemExists = collection.items.includes(currentItemForModal.id);
            let feedbackMessage = '';
            if (isChecked && !itemExists) {
                collection.items.push(currentItemForModal.id);
                feedbackMessage = `'${currentItemForModal.name}' añadido a "${collectionName}".`;
            } else if (!isChecked && itemExists) {
                collection.items = collection.items.filter(id => id !== currentItemForModal.id);
                feedbackMessage = `'${currentItemForModal.name}' quitado de "${collectionName}".`;
            }
            if (feedbackMessage) {
                saveUserProgressLocal();
                addToCollectionFeedback.textContent = feedbackMessage;
                const label = existingCollectionsListModal.querySelector(`label[for="dict-coll-modal-chk-${collection.name.replace(/\s+/g, '-')}-${currentCollectionTypeForModal}"]`);
                if (label) label.textContent = `${collection.name} (${collection.items.length})`;
                if (collection.isSystem) {
                    if (currentCollectionTypeForModal === 'vocab' && currentDetailTerm && currentDetailTerm.id === currentItemForModal.id) {
                        updateVocabLikeButtonVisual();
                    } else if (currentCollectionTypeForModal === 'kanji' && currentDetailKanji && currentDetailKanji.l === currentItemForModal.id) {
                        updateKanjiLikeButtonVisual();
                    }
                }
            }
        }
    }

    function handleCreateAndAddToNewCollectionModalDict() {
        const newName = newCollectionNameModalInput.value.trim();
        if (!newName) {
            addToCollectionFeedback.textContent = "El nombre no puede estar vacío."; return;
        }
        if (userProgress.dictionaryCollections[currentCollectionTypeForModal].find(c => c.name.toLowerCase() === newName.toLowerCase())) {
            addToCollectionFeedback.textContent = "Ya existe una colección con este nombre."; return;
        }
        if (currentItemForModal) {
            userProgress.dictionaryCollections[currentCollectionTypeForModal].push({ name: newName, items: [currentItemForModal.id], isSystem: false });
            saveUserProgressLocal();
            addToCollectionFeedback.textContent = `'${currentItemForModal.name}' añadido a la nueva colección "${newName}".`;
            newCollectionNameModalInput.value = '';
            populateExistingCollectionsInModalDict();
        }
    }

    function updateVocabLikeButtonVisual() {
        const favCollection = userProgress.dictionaryCollections.vocab.find(c => c.isSystem && c.name === "Favoritos Vocab");
        if (vocabDetailLikeButton && currentDetailTerm && favCollection) {
            vocabDetailLikeButton.classList.toggle('liked', favCollection.items.includes(currentDetailTerm.id));
        }
    }
    function updateKanjiLikeButtonVisual() {
        const favCollection = userProgress.dictionaryCollections.kanji.find(c => c.isSystem && c.name === "Favoritos Kanji (Diccionario)");
         if (kanjiDetailLikeButtonDict && currentDetailKanji && favCollection) {
            kanjiDetailLikeButtonDict.classList.toggle('liked', favCollection.items.includes(currentDetailKanji.l));
        }
    }

    // ========== INICIALIZACIÓN Y EVENT LISTENERS GLOBALES (sin cambios) ==========
    function initComponents() {
        if (searchButton) searchButton.addEventListener('click', () => performSearch());
        if (searchInput) searchInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') performSearch(); });

        if (vocabShowMoreButton) vocabShowMoreButton.addEventListener('click', renderPaginatedVocabResults);
        if (kanjiShowMoreButton) kanjiShowMoreButton.addEventListener('click', renderPaginatedKanjiResults);
        if (vocabExamplesShowMoreButton) vocabExamplesShowMoreButton.addEventListener('click', renderPaginatedExamples);
        if (kanjiCommonWordsShowMoreButton) kanjiCommonWordsShowMoreButton.addEventListener('click', () => renderPaginatedKanjiRelatedWords('common'));
        if (kanjiAllWordsShowMoreButton) kanjiAllWordsShowMoreButton.addEventListener('click', () => renderPaginatedKanjiRelatedWords('all'));

        if (searchResultsTabButtons) {
            searchResultsTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    searchResultsTabButtons.forEach(btn => btn.classList.remove('active-tab'));
                    button.classList.add('active-tab');
                    const targetSectionId = button.dataset.target;
                    document.querySelectorAll('.results-section').forEach(section => {
                        section.classList.toggle('active-results-section-mobile', section.id === targetSectionId);
                    });
                });
            });
        }
        window.addEventListener('resize', handleResponsiveTabs);


        if (vocabDetailBackButton) vocabDetailBackButton.addEventListener('click', goBack);
        if (kanjiDetailBackButton) kanjiDetailBackButton.addEventListener('click', goBack);

        if (kanjiEditNotesButton) kanjiEditNotesButton.addEventListener('click', () => {
            kanjiUserNotesTextarea.readOnly = false; kanjiUserNotesTextarea.focus();
            kanjiSaveNotesButton.classList.remove('hidden'); kanjiEditNotesButton.classList.add('hidden');
        });
        if (kanjiSaveNotesButton) kanjiSaveNotesButton.addEventListener('click', () => {
            if (currentDetailKanji) {
                userProgress.kanjiUserNotes[currentDetailKanji.l] = kanjiUserNotesTextarea.value;
                saveUserProgressLocal();
                kanjiUserNotesTextarea.readOnly = true;
                kanjiSaveNotesButton.classList.add('hidden'); kanjiEditNotesButton.classList.remove('hidden');
                kanjiResetNotesButton.disabled = !kanjiUserNotesTextarea.value;
            }
        });
       if (kanjiResetNotesButton) kanjiResetNotesButton.addEventListener('click', () => {
            if (currentDetailKanji && confirm("¿Seguro que quieres borrar tu nota personal para este kanji?")) {
                kanjiUserNotesTextarea.value = ''; delete userProgress.kanjiUserNotes[currentDetailKanji.l];
                saveUserProgressLocal();
                kanjiUserNotesTextarea.readOnly = true;
                kanjiSaveNotesButton.classList.add('hidden'); kanjiEditNotesButton.classList.remove('hidden');
                kanjiResetNotesButton.disabled = true;
            }
        });

        if (vocabDetailLikeButton) vocabDetailLikeButton.addEventListener('click', () => {
            const favCollection = userProgress.dictionaryCollections.vocab.find(c => c.isSystem && c.name === "Favoritos Vocab");
            if (currentDetailTerm && favCollection) {
                currentItemForModal = { type: 'vocab', id: currentDetailTerm.id, name: (currentDetailTerm.KJ[0]?.tx || currentDetailTerm.KN[0]?.tx) };
                currentCollectionTypeForModal = 'vocab';
                toggleItemInExistingCollectionDict(favCollection.name, !favCollection.items.includes(currentDetailTerm.id));
            }
        });
        if (vocabDetailAddCollectionButton) vocabDetailAddCollectionButton.addEventListener('click', () => {
            if (currentDetailTerm) openModalForCollection('vocab', currentDetailTerm.id, currentDetailTerm.KJ[0]?.tx || currentDetailTerm.KN[0]?.tx);
        });

        if (kanjiDetailLikeButtonDict) kanjiDetailLikeButtonDict.addEventListener('click', () => {
            const favCollection = userProgress.dictionaryCollections.kanji.find(c => c.isSystem && c.name === "Favoritos Kanji (Diccionario)");
            if (currentDetailKanji && favCollection) {
                currentItemForModal = { type: 'kanji', id: currentDetailKanji.l, name: currentDetailKanji.l };
                currentCollectionTypeForModal = 'kanji';
                toggleItemInExistingCollectionDict(favCollection.name, !favCollection.items.includes(currentDetailKanji.l));
            }
        });
        if (kanjiDetailAddCollectionButtonDict) kanjiDetailAddCollectionButtonDict.addEventListener('click', () => {
            if (currentDetailKanji) openModalForCollection('kanji', currentDetailKanji.l, currentDetailKanji.l);
        });

        if (addToCollectionModalClose) addToCollectionModalClose.addEventListener('click', () => addToCollectionModal.classList.add('hidden'));
        if (createAndAddToNewCollectionBtnModal) createAndAddToNewCollectionBtnModal.addEventListener('click', handleCreateAndAddToNewCollectionModalDict);

        window.addEventListener('click', (event) => {
            if (event.target === addToCollectionModal) addToCollectionModal.classList.add('hidden');
        });

        if (searchOriginalQueryLink) searchOriginalQueryLink.addEventListener('click', (e) => {
            e.preventDefault();
            const originalTermToSearch = searchOriginalQueryLink.textContent;
            if (originalTermToSearch) {
                searchInput.value = originalTermToSearch;
                performSearch(true);
            }
        });
    }

    // --- Arranque ---
    await loadAllDictionaryData();

    const navigatedByHashOnLoad = await handleDictionaryNavigation(); // await
    if (!navigatedByHashOnLoad && !document.querySelector('.active-dict-view') && mainView) {
        showView('main');
    }

    window.addEventListener('hashchange', handleDictionaryNavigation);

    handleResponsiveTabs();

});