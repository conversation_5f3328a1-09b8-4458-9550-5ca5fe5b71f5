<?php
// ========== search.php ==========

// --- CONFIGURACIÓN DE LA BASE DE DATOS ---
$host = 'srv944.hstgr.io';
$port = 3306;
$dbname = 'u636704306_Dictionaries';
$user = 'u636704306_Kevs';
$pass = '5@8W>|NRe';
$charset = 'utf8mb4';

// --- CONFIGURACIÓN DE LA CONEXIÓN (PDO) ---
$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (\PDOException $e) {
    // En un entorno de producción, registrar el error en lugar de mostrarlo
    http_response_code(500);
    echo json_encode(['error' => 'Error de conexión a la base de datos.']);
    // Log a más detalle: throw new \PDOException($e->getMessage(), (int)$e->getCode());
    exit;
}

// --- RESPUESTA JSON ---
header('Content-Type: application/json; charset=utf-8');

// --- OBTENER PARÁMETROS DE LA SOLICITUD ---
$query = isset($_GET['query']) ? trim($_GET['query']) : '';
$searchAsJapanese = isset($_GET['searchAsJapanese']) ? filter_var($_GET['searchAsJapanese'], FILTER_VALIDATE_BOOLEAN) : false;

if (empty($query)) {
    echo json_encode(['vocab' => [], 'kanji' => []]);
    exit;
}

// --- INICIALIZAR RESULTADOS ---
$response = [
    'vocab' => [],
    'kanji' => []
];

// --- PREPARAR TÉRMINO DE BÚSQUEDA ---
$likeQuery = '%' . $query . '%';
// Patrón para búsqueda de palabra completa (con límites de palabra)
$wordBoundaryPattern = '[[:<:]]' . preg_quote($query, '') . '[[:>:]]';

// ========== BÚSQUEDA DE VOCABULARIO (registros_full) ==========
if ($searchAsJapanese) {
    // Buscar en columnas de kanji o kana
    $sqlVocab = "SELECT * FROM registros_full WHERE KJ_tx LIKE ? OR KN_tx LIKE ?";
    $stmtVocab = $pdo->prepare($sqlVocab);
    $stmtVocab->execute([$likeQuery, $likeQuery]);
} else {
    // Buscar en columnas de significados en español o inglés con ordenamiento especial
    // Priorizar resultados donde el término aparece en la primera definición
    // Usar búsqueda de palabra completa para evitar coincidencias parciales (ej: "agua" en "paraguas")
    $sqlVocab = "
        SELECT *,
        CASE
            -- Prioridad 1: Término aparece como palabra completa en la primera definición de español
            WHEN SE_GL_2_tx REGEXP ? AND LOCATE('|', SE_GL_2_tx) > 0 AND
                 SUBSTRING(SE_GL_2_tx, 1, LOCATE('|', SE_GL_2_tx) - 1) REGEXP ? THEN 1
            -- Prioridad 2: Término aparece como palabra completa en la primera definición de español (sin separador |)
            WHEN SE_GL_2_tx REGEXP ? AND LOCATE('|', SE_GL_2_tx) = 0 THEN 1
            -- Prioridad 3: Término aparece como palabra completa en la primera definición de inglés
            WHEN SE_GL_1_tx REGEXP ? AND LOCATE('|', SE_GL_1_tx) > 0 AND
                 SUBSTRING(SE_GL_1_tx, 1, LOCATE('|', SE_GL_1_tx) - 1) REGEXP ? THEN 2
            -- Prioridad 4: Término aparece como palabra completa en la primera definición de inglés (sin separador |)
            WHEN SE_GL_1_tx REGEXP ? AND LOCATE('|', SE_GL_1_tx) = 0 THEN 2
            -- Prioridad 5: Término aparece como palabra completa en definiciones posteriores de español
            WHEN SE_GL_2_tx REGEXP ? THEN 3
            -- Prioridad 6: Término aparece como palabra completa en definiciones posteriores de inglés
            WHEN SE_GL_1_tx REGEXP ? THEN 4
            ELSE 5
        END as definition_priority
        FROM registros_full
        WHERE SE_GL_2_tx REGEXP ? OR SE_GL_1_tx REGEXP ?
        ORDER BY definition_priority ASC";

    $stmtVocab = $pdo->prepare($sqlVocab);
    $stmtVocab->execute([
        $wordBoundaryPattern, $wordBoundaryPattern, // Para prioridad 1
        $wordBoundaryPattern,                       // Para prioridad 2
        $wordBoundaryPattern, $wordBoundaryPattern, // Para prioridad 3
        $wordBoundaryPattern,                       // Para prioridad 4
        $wordBoundaryPattern, $wordBoundaryPattern, // Para prioridad 5 y 6
        $wordBoundaryPattern, $wordBoundaryPattern  // Para la condición WHERE
    ]);
}
$response['vocab'] = $stmtVocab->fetchAll();


// ========== BÚSQUEDA DE KANJI (kanji_dict) ==========
if ($searchAsJapanese) {
    // Si la búsqueda es un solo caracter, buscar coincidencia exacta en el kanji
    if (mb_strlen($query, 'UTF-8') === 1) {
         $sqlKanji = "SELECT * FROM kanji_dict WHERE li_tx = ? OR rM_g_rd_on_vl LIKE ? OR rM_g_rd_kun_vl LIKE ?";
         $stmtKanji = $pdo->prepare($sqlKanji);
         $stmtKanji->execute([$query, $likeQuery, $likeQuery]);
    } else {
        // Buscar en lecturas on/kun para búsquedas más largas
        $sqlKanji = "SELECT * FROM kanji_dict WHERE rM_g_rd_on_vl LIKE ? OR rM_g_rd_kun_vl LIKE ?";
        $stmtKanji = $pdo->prepare($sqlKanji);
        $stmtKanji->execute([$likeQuery, $likeQuery]);
    }
} else {
    // Buscar en significados en español o inglés
    $sqlKanji = "SELECT * FROM kanji_dict WHERE mn_ln_es_vl LIKE ? OR mn_ln_en_vl LIKE ?";
    $stmtKanji = $pdo->prepare($sqlKanji);
    $stmtKanji->execute([$likeQuery, $likeQuery]);
}
$response['kanji'] = $stmtKanji->fetchAll();


// --- DEVOLVER LA RESPUESTA JSON ---
echo json_encode($response);
?>